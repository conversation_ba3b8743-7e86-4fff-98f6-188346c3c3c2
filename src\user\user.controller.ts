import { Controller, Get, Post, Body, Patch, Param, Delete, Response, ValidationPipe, UsePipes, Headers, Query, UseGuards } from '@nestjs/common';
import { UserService } from './user.service';
import { Constants } from 'src/Constants';
import { ApiExtraModels, ApiResponse } from '@nestjs/swagger';
import { LoginDto, SaveForegroundBackgroundActivityDto, SavePoS3UrlsDto, SavePricingFeedbackDto, SaveShareProductPricing, SaveShareWidgetRequest, SaveTooltipDto, SaveVideoViewDto, SaveWidgetTermsCondtionUserActionsDto, ShareWidgetRequestSwaggerDto, SignedUrl, ConfirmSignupDataDto, UserSignUpDto, UserGlobalSignoutDto, SaveBomUpload, SaveSelectedBomProducts, SaveGameScore, SaveBomDraftDto, SaveBomSummaryDto, SaveBomHeaderDetailsDto, BomUploadResultDto } from './dto/user.dto';
import { PricingFeedbackSwaggerEntity, SwaggerErrorResponse, SwaggerStringResponse, UserBuyingPreferenceSwaggerEntity, UserSellingPreferenceSwaggerEntity, UserSwaggerEntity } from '@bryzos/extended-widget-library';
import { DeleteResaleCertificateDto, SaveBuyingPreferenceDto, SaveBuyerProfileDto, SaveBuyerCompanyInfoDto, SaveBuyerDeliveryDto, SaveBuyerReceivingHrsDto, SaveBnplRequestDto, SaveBuyerDucumentLibDto, SaveIncreaseCreditLimitDto, SaveDepositAmountDto } from './dto/save-buying-preference.dto';
import { UserBuyingPreferenceService } from './user-buying-preference/user-buying-preference.service';
import { SaveSellingPreferenceDto, saveCassSellerDto, SaveSellerProfileViaMobileDto, SaveSellerCompanyViaMobileDto, SaveSellerStockInformationDto, SaveFundingSettingsDto } from './dto/save-selling-preference.dto';
import { UserSellingPreferenceService } from './user-selling-preference/user-selling-preference.service';
import { SavePORatingsDto } from 'src/purchase-order/dto/save-po-ratings.dto';
import { SaveStatezipDto, SaveOnBoardDto, VerifyUserEmailDto,VerifyZipCodeDto, GetVideosDto, SaveShareVideoAnalyticsDto, SaveMigrateToPasswordLessDto } from './dto/user.dto';
import { ChannelAddUsersDto, ChatRoomDto, CreateChannelAddUsersDto, CreateChatRoomDto } from './dto/chat.dto';
import { CreateUserDto } from './dto/create-user.dto';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
const errorTag = Constants.ERROR_TAG;
import { Roles, RolesGuard } from '@bryzos/base-library';


@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService,
    private readonly userBuyingPreference: UserBuyingPreferenceService,
    private readonly userSellingPreference: UserSellingPreferenceService,) {}

  @ApiResponse({
    description: 'User Login',
    type: UserSwaggerEntity,
    status: 200
  })
  @Post('/login')
  @UsePipes(ValidationPipe)
  async login(@Body() userLoginDto: LoginDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let superAdminUserId = res.locals.superAdminUserId;
    let payloadData = userLoginDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.login(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save pricing feedback',
    type: PricingFeedbackSwaggerEntity,
    status: 200
  })
  @Post('/savePricingFeedback')
  @UsePipes(ValidationPipe)
  async savePricingFeedback(@Body() pricingFeedback: SavePricingFeedbackDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = pricingFeedback[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.savePricingFeedback(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    status: 200,
    description: 'Save share widget request',
    type: ShareWidgetRequestSwaggerDto,
  })
  @Post('/shareWidgetRequest')
  @UsePipes(ValidationPipe)
  async shareWidget(@Body() shareWidetRequestDto: SaveShareWidgetRequest, @Response() res) {
    let superAdminUserId = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = shareWidetRequestDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveShareWidgetRequest(userId, payloadData,superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  // @Get('/validateZip/:zip')
  // async validateZipCode(@Param('zip') zip: string, @Response() res) {
  //   let responseData = {
  //     [responseTag]: await this.userService.validateZipCode(zip)
  //   };
  //   res.locals.responseData = responseData;
  //   return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  // }

  @ApiResponse({
    status: 200,
    description: 'Log-out user',
    type: SwaggerStringResponse,
  })
  @Post('/logout')
  @UsePipes(ValidationPipe)
  async logout(@Body() userLoginDto: LoginDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let superAdminUserId = res.locals.superAdminUserId;
    let payloadData = userLoginDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.logout(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    status: 200,
    description: 'Share Product price',
    type: SwaggerStringResponse,
  })
  @Post('/shareProductPrice')
  @UsePipes(ValidationPipe)
  async shareProductPricing(@Body() shareProductPricingDto: SaveShareProductPricing, @Response() res) {
    let superAdminUserId = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = shareProductPricingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.shareProductPricing(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    status: 200,
    description: 'Save terms and condition',
    type: SwaggerStringResponse,
  })
  @Post('/saveTermsCondition')
  @UsePipes(ValidationPipe)
  async saveAcceptedTermsCondition(@Body() widgetTermsConditionDto: SaveWidgetTermsCondtionUserActionsDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = widgetTermsConditionDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveAcceptedTermsConditions(userId, payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiExtraModels(SwaggerErrorResponse)
  @ApiResponse({
    description: 'Save Buying Preferences',
    type: UserBuyingPreferenceSwaggerEntity,
    status: 200
  })
  @Post('/save/buyingPreference')
  @UsePipes(ValidationPipe)
  async saveBuyingPreference(@Body() buyingPreferenceDto: SaveBuyingPreferenceDto, @Response() res){
    let superAdminUserId  = res.locals.superAdminUserId ;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyingPreferenceDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.create(payloadData, userId,superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Get Buying Preferences',
    type: UserBuyingPreferenceSwaggerEntity,
    status: 200
  })
  @Get('/buyingPreference')
  async getBuyingPreference(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userBuyingPreference.getBuyingPreferenceData(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save selling Preferences',
    type: UserSellingPreferenceSwaggerEntity,
    status: 200
  })
  @Post('/save/sellingPreference')
  @UsePipes(ValidationPipe)
  async saveSellingPreference(@Body() sellingPreferenceDto: SaveSellingPreferenceDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = sellingPreferenceDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.create(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Get selling Preferences',
    type: UserSellingPreferenceSwaggerEntity,
    status: 200
  })
  @Get('/sellingPreference')
  async getSellingPreference(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userSellingPreference.getSellingPreferenceData(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    status: 200,
    description: 'Get S3 Signed URL',
    type: SwaggerStringResponse
  })
  @Post('/get_signed_url')
  @UsePipes(ValidationPipe)
  async getSignedS3Url(@Body() dto: SignedUrl,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = dto[payloadTag];

    let responseData = {
      [responseTag]: await this.userService.getSignedS3Url(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @ApiResponse({
    description: 'Save Purchase Order ratings',
    type: SwaggerStringResponse,
    status: 200
  })
  @UsePipes(ValidationPipe)
  @Post('/save/purchaseOrderRatings')
  async savePurchaseOrderRatings(@Body() savePoRatingDto: SavePORatingsDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = savePoRatingDto[payloadTag];
    payloadData.user_type = Constants.BUYER;
    let responseData = {
      [responseTag]: await this.userService.savePORatings(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save upload po s3 urls',
    type: SwaggerStringResponse,
    status: 200
  })
  @UsePipes(ValidationPipe)
  @Post('/save/uploadPoS3Url')
  async saveuploadPoS3Urls(@Body() savePoS3Url: SavePoS3UrlsDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = savePoS3Url[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveuploadPoS3Urls(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save Sales Order ratings',
    type: SwaggerStringResponse,
    status: 200
  })
  @UsePipes(ValidationPipe)
  @Post('/save/salesOrderRatings')
  async saveSalesOrderRatings(@Body() savePoRatingDto: SavePORatingsDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = savePoRatingDto[payloadTag];
    payloadData.user_type = Constants.SELLER;
    let responseData = {
      [responseTag]: await this.userService.savePORatings(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save upload so s3 urls',
    type: SwaggerStringResponse,
    status: 200
  })
  @UsePipes(ValidationPipe)
  @Post('/save/uploadSoS3Url')
  async saveUploadSoS3Urls(@Body() savePoS3Url: SavePoS3UrlsDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = savePoS3Url[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveUploadSoS3Urls(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  // @Get('usersList')
  // async getUsersList(@Response() res) {
  //   let responseData = {
  //     [responseTag]: await this.userService.generateExcelFile()
  //   };
  //   return res.download(responseData.data);
  // }

  @Get('purchaseOrderExcel')
  async getPurchaseOrder(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.generatePurchaseOrderExcelFile(userId)
    };
    
    if(!responseData.data.hasOwnProperty('error_message')){
      const fileName = 'Bryzos_Purchase_Order_History';
      const dateObject = new Date();
      const dateString = dateObject.toLocaleDateString('en-US',{month: '2-digit', day: '2-digit', year: '2-digit' }).replace(/\//g, '-');
      const fileNameWithDate = `${fileName}_${dateString}.xlsx`;
      return res.download(responseData.data,fileNameWithDate);
    }else{
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }

  @Get('salesOrderExcel')
  async getSalesOrder(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.generateSalesOrderExcelFile(userId)
    };

    if(!responseData.data.hasOwnProperty('error_message')){
        const fileName = 'Bryzos_Sales_Order_History';
        const dateObject = new Date();
        const dateString = dateObject.toLocaleDateString('en-US',{month: '2-digit', day: '2-digit', year: '2-digit' }).replace(/\//g, '-');
        const fileNameWithDate = `${fileName}_${dateString}.xlsx`;
        return res.download(responseData.data,fileNameWithDate);
      }else{
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }

  @Get('payableStatementExcel')
  async getpayableStatement(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.generatePayableStatementExcelFile(userId)
    };
    
    if(!responseData.data.hasOwnProperty('error_message')){
        const fileName = 'Bryzos_Accounts_Payable_Statement';
        const dateObject = new Date();
        const dateString = dateObject.toLocaleDateString('en-US',{month: '2-digit', day: '2-digit', year: '2-digit' }).replace(/\//g, '-');
        const fileNameWithDate = `${fileName}_${dateString}.xlsx`;
        return res.download(responseData.data,fileNameWithDate);
      }else{
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }

  @Get('receivableStatementExcel')
  async getreceivableStatement(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.generateReceivableStatementExcelFile(userId)
    };
    
    if(!responseData.data.hasOwnProperty('error_message')){
      const fileName = 'Bryzos_Accounts_Receivable_Statement';
      const dateObject = new Date();
      const dateString = dateObject.toLocaleDateString('en-US',{month: '2-digit', day: '2-digit', year: '2-digit' }).replace(/\//g, '-');
      const fileNameWithDate = `${fileName}_${dateString}.xlsx`;
      return res.download(responseData.data,fileNameWithDate);
    }else{
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
  }

  @Get('getResaleListOfBuyer/:POnumber')
  async getByPOnumber(@Param('POnumber') POnumber: string,@Response() res){
    let responseData = {
      [responseTag]: await this.userService.getResaleCertByPONumber(POnumber)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getProductTagMapping')
  async getUserProductTagMapping(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.getUserProductTagMapping(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('checkStateZip')
  @UsePipes(ValidationPipe)
  async checkStateZip( @Body() stateZip:SaveStatezipDto,@Response() res){
    let payloadData = stateZip[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.validateStateZip(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('create_po_open_close')
  async saveCreatePoOpenClose(@Body() saveCreatePoOpenClose,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveCreatePoOpenClose[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveCreatePoOpenClose(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('create_po_search')
  async saveCreatePoSearch(@Body() saveCreatePoSearch ,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveCreatePoSearch[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveCreatePoSearch(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getCassAccessToken')
  async getCassAccessToken(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.getCassAccessToken(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('/saveCassSeller')
  async saveCassSeller(@Body() saveCassSellerDto: saveCassSellerDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveCassSellerDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveCassSeller(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('onBoard')
  @UsePipes(ValidationPipe)
  async saveOnBoardUserRequest( @Body() onBoardDto:SaveOnBoardDto,@Response() res){
    let payloadData = onBoardDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveOnBoardUserRequest(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('verifyOnBoardUserEmail')
  @UsePipes(ValidationPipe)
  async verifyOnBoardUserEmail( @Body() userEmailDto:VerifyUserEmailDto,@Response() res){
    let payloadData = userEmailDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.checkUserEmailAlreadyExist(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('postTooltip')
  async postTooltip(@Body() tooltipDto : SaveTooltipDto, @Response() res)
  {
    let userId = res.locals.authorizedUserId;

    let payloadData = tooltipDto[payloadTag];

    let responseData = {
      [responseTag]: await this.userService.saveTooltip(payloadData, userId)
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);

  }

  @Get('getTooltip')
  async getTooltip( @Response() res)
  {
    let userId = res.locals.authorizedUserId;

    let responseData = {
          [responseTag]: await this.userService.getTooltip(userId)
        };
        res.locals.responseData = responseData;
        return res.status(Constants.RESPONSE_CODE_OK).json(responseData);

  }

@Post('/deleteResaleCert')
@UsePipes(ValidationPipe)
async DeleteResaleCertificateDto( @Body() deleteResaleCertificateDto:DeleteResaleCertificateDto, @Response() res){
  
    let payloadData = deleteResaleCertificateDto[payloadTag]; 
    const certId = payloadData.cert_id;

     let responseData = {
       [responseTag] : await this.userService.deleteResaleCertificate(certId)
     };
     res.locals.responseData = responseData;
     return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('saveCreatePoLineData')
  async savePoLineDetails(@Body() savePoLineDetails ,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = savePoLineDetails[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.savePoLineDetails(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('verifyZipCode')
  @UsePipes(ValidationPipe)
  async verifyZipCode( @Body() userZipCodeDto:VerifyZipCodeDto,@Response() res){
    let payloadData = userZipCodeDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.checkZipCodeExist(payloadData.zip_code)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('company')
  async getMainCompanyList(@Response() res){
    let responseData = {
      [responseTag]: await this.userService.getMainCompanyList()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('saveSearchPriceData')
  async saveSearchPriceData(@Body() saveSearchPriceData ,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveSearchPriceData[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveSearchPriceData(payloadData,userId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('saveSearchPriceMoveOutScreen')
  async saveSearchPriceMoveOutScreen(@Body() saveSearchPriceData ,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveSearchPriceData[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveSearchPriceMoveOutScreen(payloadData,userId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/delete')
  async deleteUser( @Response() res){
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag] : await this.userService.deleteUser(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  
  @ApiResponse({
    description: 'Signup new user',
    type: SwaggerStringResponse,
    status: 200
  })
  @UsePipes(ValidationPipe)
  @Post('/signup')
  async userSignup(@Body() userSignup: UserSignUpDto,@Response() res) {
    let payloadData = userSignup[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.userSignup(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //////////////////////////////// mobile api's//////////////////////////////
  @Post('/mobile/seller/saveProfile')
  @UsePipes(ValidationPipe)
  async saveSellerProfile(@Body() saveSellerProfileViaMobile : SaveSellerProfileViaMobileDto ,@Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = saveSellerProfileViaMobile[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveSellerProfile(payloadData,userId,superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/seller/saveCompany')
  @UsePipes(ValidationPipe)
  async saveSellerCompany(@Body() saveSellerCompanyViaMobile : SaveSellerCompanyViaMobileDto ,@Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = saveSellerCompanyViaMobile[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveSellerCompany(payloadData,userId,superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/seller/saveStockInformation')
  @UsePipes(ValidationPipe)
  async saveSellerStockInformation(@Body() saveSellerStockInformation : SaveSellerStockInformationDto ,@Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = saveSellerStockInformation[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveSellerStockInformation(payloadData,userId,superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/seller/saveFundingSettings')
  @UsePipes(ValidationPipe)
  async saveFundingSettings(@Response() res, @Body() saveFundingSettings:SaveFundingSettingsDto) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = saveFundingSettings[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveFundingSettings(userId, payloadData,superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/seller/saveDocument')
  @UsePipes(ValidationPipe)
  async saveSellerDocument(@Response() res, @Body() payload) {
    let superAdminUserId = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = payload[payloadTag];
    let responseData = {
      [responseTag]: await this.userSellingPreference.saveSellerDocument(userId, payloadData,superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveProfile')
  @UsePipes(ValidationPipe)
  async saveBuyerProfile(@Body() buyingPreferenceDto: SaveBuyerProfileDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyingPreferenceDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveBuyerProfile(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveCompanyInfo')
  @UsePipes(ValidationPipe)
  async saveBuyerCompanyInfo(@Body() buyerCompanyDto: SaveBuyerCompanyInfoDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyerCompanyDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveBuyerCompanyInfo(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveDeliveryInfo')
  @UsePipes(ValidationPipe)
  async saveBuyerDeliverInfo(@Body() buyerDeliverAddDto: SaveBuyerDeliveryDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyerDeliverAddDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveBuyerDeliverAddInfo(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveBnplRequest')
  @UsePipes(ValidationPipe)
  async saveBnplRequest(@Body() saveBnplRequestDto:SaveBnplRequestDto ,@Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = saveBnplRequestDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveBnplRequest(userId, payloadData, superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveDefaultPaymentMethod')
  @UsePipes(ValidationPipe)
  async saveDefaultPaymentMethod(@Body() payload ,@Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = payload[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveDefaultPaymentMethod(userId, payloadData, superAdminUserId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveReceivingHrsInfo')
  @UsePipes(ValidationPipe)
  async saveReceivingHrsInfo(@Body() buyerReceivingHrsDto: SaveBuyerReceivingHrsDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyerReceivingHrsDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveReceivingHrsInfo(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mobile/buyer/saveDocument')
  @UsePipes(ValidationPipe)
  async savedocumentLibrary(@Body() buyerDocumentLibDto: SaveBuyerDucumentLibDto, @Response() res) {
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let payloadData = buyerDocumentLibDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveDocumentLibrary(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  //////////////////////////////// mobile api's//////////////////////////////
  
  @Post('/createPdf')
  async createPdf(@Body() pdfData, @Response() res) {
    const data = await this.userService.createPdf(pdfData);

    if (typeof data === "object" && data.hasOwnProperty("error_message")) {
      res.setHeader("Content-Type", "application/json");
      let responseData = {
        ['data']: data
      };
      res.locals.responseData = responseData;
      return res.status(200).send(responseData);
    } else {
      res.setHeader("Content-Type", "application/pdf");
      res.locals.responseData = data;
      return res.status(200).send(data);
    }
  }

  @Get('discountData')
  async userSpreadData( @Response() res)
  {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.userSpreadData(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //////////////////////////////// Deadsimplechat api's//////////////////////////////
/*   @Post('/createRoom')
  @UsePipes(ValidationPipe)
  async createRoom(@Response() res, @Body() payload: CreateChatRoomDto) {
    let payloadData:ChatRoomDto = payload[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.createRoom(payloadData),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  } */

  @Post('/createUser')
  @UsePipes(ValidationPipe)
  async createUser(@Response() res) {
    let userId = res.locals.authorizedUserId;
    const responseData = {
      [responseTag]: await this.userService.createUser(userId),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  /* @Post('/createChannelAddUsers')
  @UsePipes(ValidationPipe)
  async createChannelAddUsers(@Response() res, @Body() payload: CreateChannelAddUsersDto) {
    let payloadData:ChannelAddUsersDto = payload[payloadTag];

    let responseData = {
      [responseTag]: await this.userService.createChannelAddUsers(payloadData),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/deleteChatRoom')
  @UsePipes(ValidationPipe)
  async deleteChatRoom(@Response() res) {
    let responseData = {
      [responseTag]: await this.userService.deleteChatRoom(),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/blockUserInChannel')
  @UsePipes(ValidationPipe)
  async blockUserInChannel(@Response() res, @Body() payload: {channel_name: string, unique_user_identifier: string}) {
    let responseData = {
      [responseTag]: await this.userService.blockUserInChannel(payload.channel_name, payload.unique_user_identifier),
    };
  
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  @Post('/unBlockUserInChannel')
  @UsePipes(ValidationPipe)
  async unBlockUserInChannel(@Response() res, @Body() payload: {channel_name: string, unique_user_identifier: string}) {
    let responseData = {
      [responseTag]: await this.userService.unBlockUserInChannel(payload.channel_name, payload.unique_user_identifier),
    };
  
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/allUsersInChannel')
  @UsePipes(ValidationPipe)
  async getAllUsersInChannel(@Response() res, @Body() payload: any) {
    let responseData = {
      [responseTag]: await this.userService.getAllUsersInChannel(payload.channel_name),
    };
  
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getAllAcceptedBuyerOrders')
  @UsePipes(ValidationPipe)
  async getAllAcceptedBuyerOrders(@Response() res, @Body() payload: any) {
    let userId = res.locals.authorizedUserId;

    let responseData = {
      [responseTag]: await this.userService.getAllAcceptedBuyerOrders(userId),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getAllAcceptedSellerOrders')
  @UsePipes(ValidationPipe)
  async getAllAcceptedSellerOrders(@Response() res, @Body() payload: any) {
    let userId = res.locals.authorizedUserId;

    let responseData = {
      [responseTag]: await this.userService.getAllAcceptedSellerOrders(userId),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  } */
  //////////////////////////////// Deadsimplechat api's//////////////////////////////
  
  @Post('/saveForegroundBackgroundActivity')
  @UsePipes(ValidationPipe)
  async saveForegroundBackgroundActivity(@Body() userLoginDto: SaveForegroundBackgroundActivityDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let superAdminUserId = res.locals.superAdminUserId;
    let payloadData = userLoginDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveForegroundBackgroundActivity(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getVideos')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getVideos(@Query() query: GetVideosDto, @Response() res) {
    const tagsArray = query.tag.split('|');

    // Pass tagsArray to your service
    const videos = await this.userService.getVideos(tagsArray);

    const responseData = {
      [responseTag]: videos,
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveVideoViewCount')
  @UsePipes(ValidationPipe)
  async saveVideoViewCount(@Body() videoDto: SaveVideoViewDto, @Response() res) {
    let payloadData = videoDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveVideoViewCount(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/confirm-signup')
  @UsePipes(ValidationPipe)
  async confirmSignup(@Body() confirmSignupDto: ConfirmSignupDataDto, @Response() res) {
    let username = confirmSignupDto[payloadTag].username;
    let responseData = {
      [responseTag]: await this.userService.confirmUser(username)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //This API is used for existing users or user made from "create user" screen. This is an open API
  @Post('migratedToPasswordless')
  @UsePipes(ValidationPipe)
  async migratedToPasswordless(@Body() saveMigrateToPasswordLess : SaveMigrateToPasswordLessDto, @Response() res) {
    const payload = saveMigrateToPasswordLess[payloadTag]
    let responseData = {
      [responseTag]: await this.userService.migratedToPasswordless(payload)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('globalSignout')
  @UsePipes(ValidationPipe)
  async globalSignout(@Body() userglobalSignoutDto : UserGlobalSignoutDto, @Response() res) {
    const payload = userglobalSignoutDto[payloadTag]
    let responseData = {
      [responseTag]: await this.userService.globalSignout(payload)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveShareVideo')
  async saveShareVideo(@Body() shareVideo, @Response() res) {
    let responseData = {
      [responseTag]: await this.userService.saveShareVideo()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveShareVideoAnalytics')
  @UsePipes(ValidationPipe)
  async saveShareVideoAnalytics(@Body() shareVideoAnalytics: SaveShareVideoAnalyticsDto, @Response() res) {
    let payloadData = shareVideoAnalytics[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.saveShareVideoAnalytics(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/shuffleSequence')
  async shuffleSequence( @Response() res ) {
    const videos = await this.userService.shuffleSequence();
    const responseData = {
      [responseTag]: videos,
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('check-approval/:emailId')
  async checkUserApproval(@Param('emailId') emailId: string, @Response() res) {
    let responseData = {
      [responseTag]: await this.userService.checkUserApproval(emailId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveBuyNowPayLaterData')
  @UsePipes(ValidationPipe)
  async saveBuyNowPayLaterData(@Body() saveBnplRequestDto:SaveBnplRequestDto ,@Response() res){
    let superAdminUserId  = res.locals.superAdminUserId;
    let payloadData = saveBnplRequestDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userBuyingPreference.saveBnplRequest(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getAccessToken')
  async getAccessToken(@Response() res){
    const accessToken = await this.userService.getAccessToken();
    const responseData = {
      [responseTag]: accessToken,
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('/increaseCreditLimitRequest')
  @UsePipes(ValidationPipe)
  async increaseCreditLimitRequest(@Body() saveIncreaseCreditLimitDto:SaveIncreaseCreditLimitDto ,@Response() res){
    let superAdminUserId  = res.locals.superAdminUserId;
    let payloadData = saveIncreaseCreditLimitDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userBuyingPreference.increaseCreditLimitRequest(payloadData, userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/cancelIncreaseCreditLimitRequest')
  @UsePipes(ValidationPipe)
  async cancelIncreaseCreditLimitRequest(@Response() res){
    let superAdminUserId  = res.locals.superAdminUserId;
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userBuyingPreference.cancelIncreaseCreditLimitRequest(userId, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/deposit')
  @UsePipes(ValidationPipe)
  async widgetCalculateDepositAmount(@Body() saveDepositAmountDto:SaveDepositAmountDto ,@Response() res){
    let payloadData = saveDepositAmountDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userBuyingPreference.getDepositAmount(payloadData,userId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('bryzosStatesResaleCertUrl')
  async getBryzosStatesResaleCertUrl( @Response() res)
  {
    let responseData = {
      [responseTag]: await this.userService.getBryzosStatesCertUrl()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('buyer/w9form')
  async getBryzosW9FormUrl( @Response() res)
  {
    let responseData = {
      [responseTag]: await this.userService.getBryzosW9FormUrl()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('bom/:bomUploadId?')
  @UseGuards(RolesGuard)
  @Roles(Constants.BUYER)
  async getSavedBom(@Param('bomUploadId') bomUploadId: string, @Response() res){
    let userId = res.locals.authorizedUserId;
    let bomId = bomUploadId ? bomUploadId : null;
    let responseData = {
      [responseTag]: await this.userService.getCombinedBomAndDraftPoData(userId, bomId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/game-score')
  @UsePipes(ValidationPipe)
  async saveGameScore(@Body() saveGameScore:SaveGameScore, @Response() res){
    let payloadData = saveGameScore[payloadTag];
    let userId = res.locals.authorizedUserId;
    let superAdminUserId = res.locals.superAdminUserId;
    let responseData = {
      [responseTag]: await this.userService.saveGameScore(userId, payloadData, superAdminUserId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('score-data')
  @UsePipes(ValidationPipe)
  async getGameScore(@Response() res){
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.getGameScore(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/draft')
  @UsePipes(ValidationPipe)
  async saveBOMDraftData(@Body() saveBomDraftDto:SaveBomDraftDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveBomDraftDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveBOMDraftData(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/review-complete')
  @UsePipes(ValidationPipe)
  async saveBOMSummaryData(@Body() saveBomSummaryDto: SaveBomSummaryDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveBomSummaryDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveBOMSummaryData(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/header-details')
  @UsePipes(ValidationPipe)
  @UseGuards(RolesGuard)
  @Roles(Constants.BUYER)
  async saveBomHeaderDetails(@Body() saveBomHeaderDetailsDto: SaveBomHeaderDetailsDto,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveBomHeaderDetailsDto[payloadTag];
    let responseData = {  
      [responseTag]: await this.userService.saveBomHeaderDetails(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/extract-text-from-file')
  async textExtractFromFile(@Body() saveBomUpload: SaveBomUpload, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveBomUpload[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.textExtractFromFile(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/product-extract')
  async saveProductExtract(@Body() bomUploadResultDto: BomUploadResultDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = bomUploadResultDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveProductExtract(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/bom/selection-box-details')
  @UsePipes(ValidationPipe)
  async saveSelectionBoxDetails(@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.saveSelectionBoxDetails(userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}

